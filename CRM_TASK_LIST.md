# CRM Task List

## 1. Overall Description
- [ ] **1.1 Purpose:** Define the purpose of the CRM system.
- [ ] **1.2 Scope:** Define in-scope and out-of-scope functionalities.
- [ ] **1.3 Definitions, Acronyms, and Abbreviations:** List and define relevant terms.
- [ ] **1.4 References:** Include links to UI mockups, style guides, and standards.
- [ ] **1.5 Overview:** Describe the document organization.

## 2. Overall Description
- [ ] **2.1 Product Perspective:** Describe the CRM as a standalone web application (Laravel, MySQL).
- [ ] **2.2 Product Functions (Summary):**
    - [ ] Manage and authenticate users with role-based access.
    - [ ] View a personalized and informative dashboard.
    - [ ] Manage detailed profiles for contacts and companies.
    - [ ] Track sales opportunities through a visual pipeline.
    - [ ] Log and manage activities like emails, calls, and meetings.
    - [ ] Create and track tasks.
    - [ ] Generate basic reports on sales and activities.
    - [ ] Customize certain aspects of the CRM.
- [ ] **2.3 User Characteristics:**
    - [ ] Define Administrator role.
    - [ ] Define Sales Manager role.
    - [ ] Define Sales Representative role.
    - [ ] Note user computer literacy expectations.
- [ ] **2.4 Operating Environment:**
    - [ ] Server-side: PHP (Laravel), MySQL, Web Server (Apache/Nginx).
    - [ ] Client-side: Modern web browsers (responsive design).
    - [ ] Internet connectivity requirement.
- [x] **2.5 Design and Implementation Constraints:**
    - [x] Use Laravel (latest stable LTS) and MySQL (latest stable). (Verified)
    - [ ] Responsive UI with modern design principles.
    - [ ] Follow OWASP Top 10 security best practices.
    - [x] Support UTF-8 character encoding. (Set in AppServiceProvider)
    - [ ] Sanitize user-generated content (prevent XSS).
    - [x] Use Eloquent ORM. (Default in Laravel)
    - [ ] Use Blade templating (potentially with Alpine.js or Vue.js/Inertia.js).
- [x] **2.6 Assumptions and Dependencies:**
    - [ ] Reliable internet access for users.
    - [x] Server environment meets Laravel/MySQL requirements. (Verified by migrations)
    - [ ] Availability of third-party email services.

## 3. Specific Requirements

### 3.1 Functional Requirements

#### 3.1.1 User Management & Authentication (FR-USR)
- [ ] **FR-USR-001: User Registration:**
    - [ ] Admins can create new user accounts.
    - [ ] Input fields: First Name, Last Name, Email (unique), Password, Role.
- [ ] **FR-USR-002: User Login:**
    - [ ] Secure login mechanism (email and password).
    - [ ] Implement "remember me" functionality.
- [x] **FR-USR-003: Password Management:**
    - [ ] Secure password reset via email verification.
    - [ ] Users can change their own password when logged in.
    - [x] Passwords stored hashed. (Default Laravel behavior)
- [ ] **FR-USR-004: User Logout:** Secure user logout.
- [x] **FR-USR-005: Role-Based Access Control (ACL):**
    - [x] Predefined roles: Administrator, Sales Manager, Sales Representative. (Seeded)
    - [ ] Administrator: Full CRUD access.
    - [ ] Sales Manager: CRUD access to own/team records, view aggregated team reports.
    - [ ] Sales Representative: CRUD access primarily to own records.
    - [ ] Admins can assign roles to users.
- [ ] **FR-USR-006: User Profile Management:**
    - [ ] Users can view/edit own profile (Name, Email, password, profile picture).
- [ ] **FR-USR-007: User Deactivation/Activation:**
    - [ ] Admins can activate/deactivate user accounts.

#### 3.1.2 Dashboard (FR-DSH)
- [ ] **FR-DSH-001: Personalized Dashboard View:** Tailored to user's role and data.
- [ ] **FR-DSH-002: Configurable Widgets:** Add, remove, rearrange widgets.
- [ ] **FR-DSH-003: Key Metric Widgets:**
    - [ ] Sales Performance Overview.
    - [ ] My Open Deals Pipeline.
    - [ ] My Tasks Due Today/This Week.
    - [ ] Upcoming Meetings & Calls.
    - [ ] Recent Activity Feed.
    - [ ] Lead Status Breakdown.
- [ ] **FR-DSH-004: Quick Add Buttons:** "+ New Contact," "+ New Deal," "+ Log Activity."
- [ ] **FR-DSH-005: Interactive Elements:** Hover details, clickable elements.

#### 3.1.3 Contact Management (FR-CON)
- [ ] **FR-CON-001: Create Contact:**
    - [ ] Fields: First Name, Last Name, Email, Phone, Job Title, Company, Address, Lead Source, Lead Status, Owner, Notes, Custom Fields.
- [ ] **FR-CON-002: View Contact List:** Pagination, sorting, filtering. Customizable columns.
- [ ] **FR-CON-003: View Contact Detail:** Header, tabbed/sectioned layout (Overview, Activity, Deals, Tasks, Notes).
- [ ] **FR-CON-004: Edit Contact:** Inline editing where appropriate.
- [ ] **FR-CON-005: Delete Contact:** Soft delete preferred.
- [ ] **FR-CON-006: Import Contacts:** Admins import from CSV (field mapping).
- [ ] **FR-CON-007: Export Contacts:** Users export to CSV (based on filters).
- [ ] **FR-CON-008: Duplicate Detection:** Basic detection on creation/import.
- [ ] **FR-CON-009: Contact Activity Timeline:** Chronological, filterable timeline.

#### 3.1.4 Company (Account) Management (FR-COM)
- [ ] **FR-COM-001: Create Company:**
    - [ ] Fields: Company Name, Website, Industry, Phone, Address, Employees, Revenue, Owner, Description, Custom Fields.
- [ ] **FR-COM-002: View Company List:** Pagination, sorting, filtering.
- [ ] **FR-COM-003: View Company Detail:** Header, tabbed/sectioned layout (Overview, Activity, Contacts, Deals, Notes, Hierarchy).
- [ ] **FR-COM-004: Edit Company:**
- [ ] **FR-COM-005: Delete Company:** Soft delete.
- [ ] **FR-COM-006: Company Hierarchy:** Optional parent/subsidiary relationships.

#### 3.1.5 Deal (Opportunity) Management (FR-DEL)
- [ ] **FR-DEL-001: Create Deal:**
    - [ ] Fields: Deal Name, Company, Contact(s), Stage, Close Date, Amount, Probability, Owner, Description, Products/Services, Reason Won/Lost, Custom Fields.
- [ ] **FR-DEL-002: View Deals (Pipeline/Kanban View):** Draggable cards, stage totals.
- [ ] **FR-DEL-003: View Deals (List View):** Alternative table view.
- [ ] **FR-DEL-004: View Deal Detail:** Header, tabbed/sectioned layout (Overview, Activity, Tasks, Products, Stage History).
- [ ] **FR-DEL-005: Edit Deal:**
- [ ] **FR-DEL-006: Delete Deal:** Soft delete.
- [ ] **FR-DEL-007: Customizable Deal Stages:** Admins define stages, probabilities, colors.
- [ ] **FR-DEL-008: Deal Stage History:** Track stage movements and duration.

#### 3.1.6 Activity & Communication Tracking (FR-ACT)
- [ ] **FR-ACT-001: Log Activity:** Notes, Emails (manual), Calls, Meetings. Modal for logging.
- [ ] **FR-ACT-002: Activity Fields (Common):** Subject, Type, Date/Time, Description, Outcome, Related to, Created by.
- [ ] **FR-ACT-003: View Activities:** Visible in associated records' timelines.
- [ ] **FR-ACT-004: Edit/Delete Activity:** Permission-dependent.
- [ ] **FR-ACT-005: Email Logging (Manual/BCC):** Manual logging initially, BCC email for auto-association.
- [ ] **FR-ACT-006: Activity Reminders:** For follow-up activities.

#### 3.1.7 Task Management (FR-TSK)
- [ ] **FR-TSK-001: Create Task:**
    - [ ] Fields: Name, Description, Due Date, Priority, Status, Assigned to, Related to.
- [ ] **FR-TSK-002: View Tasks ("My Tasks" / "All Tasks"):** Dedicated page, filtering.
- [ ] **FR-TSK-003: Edit Task:**
- [ ] **FR-TSK-004: Update Task Status:**
- [ ] **FR-TSK-005: Delete Task:**
- [ ] **FR-TSK-006: Task Notifications:** Assigned, due soon, overdue.

#### 3.1.8 Reporting & Analytics (FR-RPT) - Basic for V1.0
- [ ] **FR-RPT-001: Standard Reports:**
    - [ ] Sales Pipeline Report.
    - [ ] Sales Performance Report.
    - [ ] Activity Report.
    - [ ] Lead Source Effectiveness.
- [ ] **FR-RPT-002: Report Filtering:** Date range, user, team.
- [ ] **FR-RPT-003: Report Visualization:** Basic charts.
- [ ] **FR-RPT-004: Report Export:** CSV/PDF.

#### 3.1.9 Customization & Configuration (FR-CFG)
- [ ] **FR-CFG-001: Custom Fields:** For Contacts, Companies, Deals (Text, Number, Date, Dropdown, Checkbox).
- [ ] **FR-CFG-002: Dropdown Management:** Admins customize options for system dropdowns.
- [ ] **FR-CFG-003: Email Templates (Basic):** Admins create/manage templates with placeholders.
- [ ] **FR-CFG-004: Company Profile Settings:** Admins set company name, logo, currency, timezone.

#### 3.1.10 Search & Navigation (FR-NAV)
- [ ] **FR-NAV-001: Global Search:** Across Contacts, Companies, Deals. Categorized results.
- [ ] **FR-NAV-002: Main Navigation:** Clear, persistent sidebar. Active state indication.

#### 3.1.11 Notifications (FR-NOT)
- [ ] **FR-NOT-001: In-App Notifications:** Key events, notification center.
- [ ] **FR-NOT-002: Email Notifications:** Critical events (configurable).
- [ ] **FR-NOT-003: User Notification Preferences:** Configure in-app/email notifications.

### 3.2 User Interface (UI) Requirements (UI-REQ)
- [ ] **UI-REQ-001: Modern & Clean Design:** Minimalist, ample whitespace.
- [ ] **UI-REQ-002: Responsiveness:** Desktop, tablet, mobile.
- [ ] **UI-REQ-003: Consistency:** UI elements consistent.
- [ ] **UI-REQ-004: Branding:** Admin uploads company logo. Basic theme customization (future).
- [ ] **UI-REQ-005: Typography & Iconography:** Readable, modern typography, high-quality icons.
- [ ] **UI-REQ-006: Forms:** Well-structured, clear labels, user-friendly validation.
- [ ] **UI-REQ-007: Tables & Lists:** Readable, sortable, pagination, hover actions.
- [ ] **UI-REQ-008: Modals & Popups:** For focused tasks.
- [ ] **UI-REQ-009: Empty States:** User-friendly messages with CTAs.
- [ ] **UI-REQ-010: Loading States:** Visual feedback (spinners, skeleton screens).
- [ ] **UI-REQ-011: Accessibility:** Strive for WCAG 2.1 Level AA.

### 3.3 Non-Functional Requirements (NFR)

#### 3.3.1 Performance (NFR-PERF)
- [ ] **NFR-PERF-001: Page Load Time:** < 3 seconds.
- [ ] **NFR-PERF-002: API Response Time:** < 500ms.
- [ ] **NFR-PERF-003: Concurrent Users:** Define target (e.g., 50, 100, 500).
- [ ] **NFR-PERF-004: Database Query Optimization:** Indexing, efficient queries.

#### 3.3.2 Scalability (NFR-SCAL)
- [ ] **NFR-SCAL-001: Vertical Scalability:** Allow scaling by increasing server resources.
- [ ] **NFR-SCAL-002: Horizontal Scalability (Future):** Design for future horizontal scaling.
- [ ] **NFR-SCAL-003: Data Volume:** Define targets (e.g., 1M contacts, 500k deals).

#### 3.3.3 Security (NFR-SEC)
- [ ] **NFR-SEC-001: Authentication & Authorization:** Secure auth, robust ACL.
- [ ] **NFR-SEC-002: Data Encryption:** Hashed passwords (bcrypt), HTTPS, consider encryption at rest.
- [ ] **NFR-SEC-003: Input Validation & Sanitization:** Client-side and server-side validation, output encoding.
- [ ] **NFR-SEC-004: CSRF Protection:** Implement (Laravel default).
- [ ] **NFR-SEC-005: SQL Injection Prevention:** Use Eloquent ORM, parameterized queries.
- [ ] **NFR-SEC-006: Session Management:** Secure practices.
- [ ] **NFR-SEC-007: Regular Security Audits:** Process, not system feature.
- [ ] **NFR-SEC-008: Audit Trail (Basic):** Log key user actions.

#### 3.3.4 Reliability & Availability (NFR-REL)
- [ ] **NFR-REL-001: Uptime:** Target 99.5% (excluding scheduled maintenance).
- [ ] **NFR-REL-002: Error Handling:** Graceful error handling, informative messages.
- [ ] **NFR-REL-003: Data Backup & Recovery:** Regular automated backups, recovery plan (operational).

#### 3.3.5 Maintainability (NFR-MAIN)
- [ ] **NFR-MAIN-001: Code Quality:** Well-structured, commented, follow Laravel best practices.
- [ ] **NFR-MAIN-002: Modularity:** Modular components.
- [ ] **NFR-MAIN-003: Version Control:** Use Git.
- [ ] **NFR-MAIN-004: Automated Testing:** Unit, feature tests.
- [ ] **NFR-MAIN-005: Documentation:** Technical and API documentation.

#### 3.3.6 Usability (NFR-USE)
- [ ] **NFR-USE-001: Learnability:** Basic functions learnable in <1 hour.
- [ ] **NFR-USE-002: Efficiency:** Experienced users perform tasks efficiently.
- [ ] **NFR-USE-003: User Satisfaction:** Aim for high satisfaction.
- [ ] **NFR-USE-004: Error Prevention:** UI design and system logic prevent common errors.

### 3.4 Database Requirements (DB-REQ)
- [x] **DB-REQ-001: Database System:** MySQL (latest stable). (Verified)
- [x] **DB-REQ-002: Schema Design:** Normalized (3NF). (Initial tables users, roles, role_user created)
- [x] **DB-REQ-003: Indexing:** Appropriate indexes for performance. (Primary keys and unique constraints set)
- [x] **DB-REQ-004: Data Integrity:** Foreign key constraints. (Used in role_user table)
- [x] **DB-REQ-005: Character Set:** UTF-8. (Default for migrations, AppServiceProvider ensures compatibility)
- [x] **DB-REQ-006: Migrations:** Laravel Migrations for schema changes. (Used for all schema changes)
- [x] **DB-REQ-007: Seeders:** Laravel Seeders for initial data. (RoleSeeder created and run)

### 3.5 External Interface Requirements (EIR)
- [ ] **EIR-001: Email Service Integration:** SMTP service (Mailgun, SendGrid, etc.).
- [ ] **EIR-002: API for Future Integrations (Placeholder):** Consider future RESTful API.
