<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User Management (Admin only)
    Route::middleware('role:Administrator')->group(function () {
        Route::resource('users', \App\Http\Controllers\UserController::class);
        Route::patch('users/{user}/toggle-status', [\App\Http\Controllers\UserController::class, 'toggleStatus'])->name('users.toggle-status');
    });

    // Contact Management
    Route::resource('contacts', \App\Http\Controllers\ContactController::class);

    // Company Management
    Route::resource('companies', \App\Http\Controllers\CompanyController::class);

    // Deal Management
    Route::resource('deals', \App\Http\Controllers\DealController::class);
    Route::patch('deals/{deal}/update-stage', [\App\Http\Controllers\DealController::class, 'updateStage'])->name('deals.update-stage');

    // Activity Management
    Route::resource('activities', \App\Http\Controllers\ActivityController::class);
    Route::post('activities/quick-log', [\App\Http\Controllers\ActivityController::class, 'quickLog'])->name('activities.quick-log');

    // Task Management
    Route::resource('tasks', \App\Http\Controllers\TaskController::class);
    Route::patch('tasks/{task}/complete', [\App\Http\Controllers\TaskController::class, 'complete'])->name('tasks.complete');
});

require __DIR__.'/auth.php';
