<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('website')->nullable();
            $table->string('industry')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->integer('employees')->nullable();
            $table->decimal('revenue', 15, 2)->nullable();
            $table->foreignId('owner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->text('description')->nullable();
            $table->foreignId('parent_company_id')->nullable()->constrained('companies')->onDelete('set null');
            $table->softDeletes();
            $table->timestamps();

            $table->index(['name']);
            $table->index(['industry']);
            $table->index(['owner_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
