<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('deals', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('contact_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('deal_stage_id')->constrained()->onDelete('restrict');
            $table->date('close_date')->nullable();
            $table->decimal('amount', 15, 2)->nullable();
            $table->integer('probability')->default(0); // 0-100
            $table->foreignId('owner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->text('description')->nullable();
            $table->text('products_services')->nullable();
            $table->enum('reason_won_lost', ['won', 'lost', 'pending'])->nullable();
            $table->text('reason_won_lost_description')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->index(['name']);
            $table->index(['company_id']);
            $table->index(['contact_id']);
            $table->index(['deal_stage_id']);
            $table->index(['owner_id']);
            $table->index(['close_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('deals');
    }
};
