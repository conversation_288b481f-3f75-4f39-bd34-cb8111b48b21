<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('job_title')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('set null');
            $table->text('address')->nullable();
            $table->string('lead_source')->nullable();
            $table->enum('lead_status', ['new', 'contacted', 'qualified', 'lost', 'converted'])->default('new');
            $table->foreignId('owner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->index(['first_name', 'last_name']);
            $table->index(['email']);
            $table->index(['company_id']);
            $table->index(['owner_id']);
            $table->index(['lead_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
