<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'CRM Administrator',
                'first_name' => 'CRM',
                'last_name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign Administrator role
        $adminRole = Role::where('name', 'Administrator')->first();
        if ($adminRole && !$admin->roles->contains($adminRole->id)) {
            $admin->roles()->attach($adminRole->id);
        }

        // Create sales manager user
        $salesManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Manager',
                'first_name' => 'Sales',
                'last_name' => 'Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign Sales Manager role
        $managerRole = Role::where('name', 'Sales Manager')->first();
        if ($managerRole && !$salesManager->roles->contains($managerRole->id)) {
            $salesManager->roles()->attach($managerRole->id);
        }

        // Create sales rep user
        $salesRep = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Representative',
                'first_name' => 'Sales',
                'last_name' => 'Representative',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign Sales Representative role
        $repRole = Role::where('name', 'Sales Representative')->first();
        if ($repRole && !$salesRep->roles->contains($repRole->id)) {
            $salesRep->roles()->attach($repRole->id);
        }
    }
}
