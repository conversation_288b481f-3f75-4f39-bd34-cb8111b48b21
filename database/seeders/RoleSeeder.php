<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role; // Import the Role model
use Illuminate\Support\Facades\DB; // Import DB facade for direct insertion if needed

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Role::create(['name' => 'Administrator']);
        Role::create(['name' => 'Sales Manager']);
        Role::create(['name' => 'Sales Representative']);
    }
}
