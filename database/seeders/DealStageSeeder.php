<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DealStageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = [
            ['name' => 'Lead', 'probability' => 10, 'color' => '#EF4444', 'order' => 1],
            ['name' => 'Qualified', 'probability' => 25, 'color' => '#F97316', 'order' => 2],
            ['name' => 'Proposal', 'probability' => 50, 'color' => '#EAB308', 'order' => 3],
            ['name' => 'Negotiation', 'probability' => 75, 'color' => '#3B82F6', 'order' => 4],
            ['name' => 'Closed Won', 'probability' => 100, 'color' => '#10B981', 'order' => 5],
            ['name' => 'Closed Lost', 'probability' => 0, 'color' => '#6B7280', 'order' => 6],
        ];

        foreach ($stages as $stage) {
            DB::table('deal_stages')->insert([
                'name' => $stage['name'],
                'probability' => $stage['probability'],
                'color' => $stage['color'],
                'order' => $stage['order'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
