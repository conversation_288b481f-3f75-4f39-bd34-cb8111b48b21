<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Deal Pipeline') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('deals.index', ['view' => 'list']) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    List View
                </a>
                <a href="{{ route('deals.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add New Deal
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('deals.index') }}" class="flex items-end space-x-4">
                        <input type="hidden" name="view" value="pipeline">
                        
                        <div>
                            <label for="owner_id" class="block text-sm font-medium text-gray-700">Owner</label>
                            <select name="owner_id" id="owner_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Owners</option>
                                @foreach($owners as $owner)
                                    <option value="{{ $owner->id }}" {{ request('owner_id') == $owner->id ? 'selected' : '' }}>
                                        {{ $owner->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Pipeline Board -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex space-x-6 overflow-x-auto pb-4">
                        @foreach($stages as $stage)
                            <div class="flex-shrink-0 w-80">
                                <!-- Stage Header -->
                                <div class="bg-gray-50 rounded-t-lg p-4 border-b">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-medium text-gray-900">{{ $stage->name }}</h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                                              style="background-color: {{ $stage->color }}20; color: {{ $stage->color }};">
                                            {{ $deals->get($stage->id, collect())->count() }} deals
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-500 mt-1">
                                        ${{ number_format($deals->get($stage->id, collect())->sum('amount'), 0) }} total
                                    </div>
                                </div>

                                <!-- Deal Cards -->
                                <div class="bg-gray-50 rounded-b-lg min-h-96 p-4 space-y-3" 
                                     data-stage-id="{{ $stage->id }}"
                                     ondrop="drop(event)" 
                                     ondragover="allowDrop(event)">
                                    @foreach($deals->get($stage->id, collect()) as $deal)
                                        <div class="bg-white rounded-lg shadow-sm border p-4 cursor-move deal-card" 
                                             draggable="true" 
                                             ondragstart="drag(event)" 
                                             data-deal-id="{{ $deal->id }}">
                                            <div class="flex justify-between items-start mb-2">
                                                <h4 class="font-medium text-gray-900 text-sm">{{ $deal->name }}</h4>
                                                <div class="flex space-x-1">
                                                    <a href="{{ route('deals.show', $deal) }}" class="text-blue-600 hover:text-blue-900 text-xs">View</a>
                                                    <a href="{{ route('deals.edit', $deal) }}" class="text-indigo-600 hover:text-indigo-900 text-xs">Edit</a>
                                                </div>
                                            </div>
                                            
                                            @if($deal->company)
                                                <div class="text-sm text-gray-600 mb-1">{{ $deal->company->name }}</div>
                                            @endif
                                            
                                            @if($deal->contact)
                                                <div class="text-sm text-gray-600 mb-2">{{ $deal->contact->full_name }}</div>
                                            @endif
                                            
                                            <div class="flex justify-between items-center text-sm">
                                                <span class="font-medium text-green-600">
                                                    @if($deal->amount)
                                                        ${{ number_format($deal->amount, 0) }}
                                                    @else
                                                        No amount
                                                    @endif
                                                </span>
                                                <span class="text-gray-500">
                                                    {{ $deal->close_date ? $deal->close_date->format('M d') : 'No date' }}
                                                </span>
                                            </div>
                                            
                                            @if($deal->owner)
                                                <div class="text-xs text-gray-500 mt-2">{{ $deal->owner->name }}</div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Drag & Drop JavaScript -->
    <script>
        function allowDrop(ev) {
            ev.preventDefault();
        }

        function drag(ev) {
            ev.dataTransfer.setData("text", ev.target.getAttribute('data-deal-id'));
        }

        function drop(ev) {
            ev.preventDefault();
            const dealId = ev.dataTransfer.getData("text");
            const stageId = ev.currentTarget.getAttribute('data-stage-id');
            
            // Update deal stage via AJAX
            fetch(`/deals/${dealId}/update-stage`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    deal_stage_id: stageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Move the deal card to the new stage
                    const dealCard = document.querySelector(`[data-deal-id="${dealId}"]`);
                    ev.currentTarget.appendChild(dealCard);
                    
                    // Update stage counters (you might want to reload the page or update via AJAX)
                    location.reload();
                } else {
                    alert('Error updating deal stage');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating deal stage');
            });
        }
    </script>
</x-app-layout>
