<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Contact Details: ') . $contact->full_name }}
            </h2>
            <div>
                <a href="{{ route('contacts.edit', $contact) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Edit Contact
                </a>
                <a href="{{ route('contacts.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Contacts
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Contact Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->full_name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->email ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->phone ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Job Title</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->job_title ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Company</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->company->name ?? 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Lead Source</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->lead_source ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Lead Status</dt>
                                    <dd class="text-sm text-gray-900">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            @if($contact->lead_status === 'new') bg-blue-100 text-blue-800
                                            @elseif($contact->lead_status === 'contacted') bg-yellow-100 text-yellow-800
                                            @elseif($contact->lead_status === 'qualified') bg-purple-100 text-purple-800
                                            @elseif($contact->lead_status === 'converted') bg-green-100 text-green-800
                                            @else bg-red-100 text-red-800 @endif">
                                            {{ ucfirst($contact->lead_status) }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Owner</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->owner->name ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->created_at->format('M d, Y H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd class="text-sm text-gray-900">{{ $contact->updated_at->format('M d, Y H:i') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    @if($contact->address)
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500">Address</dt>
                            <dd class="text-sm text-gray-900 mt-1">{{ $contact->address }}</dd>
                        </div>
                    @endif

                    @if($contact->notes)
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500">Notes</dt>
                            <dd class="text-sm text-gray-900 mt-1">{{ $contact->notes }}</dd>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Related Records -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Deals -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Deals ({{ $contact->deals->count() }})</h3>
                        @if($contact->deals->count() > 0)
                            <div class="space-y-2">
                                @foreach($contact->deals->take(5) as $deal)
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ $deal->name }}</div>
                                        <div class="text-gray-500">${{ number_format($deal->amount, 2) }}</div>
                                    </div>
                                @endforeach
                                @if($contact->deals->count() > 5)
                                    <div class="text-sm text-gray-500">
                                        And {{ $contact->deals->count() - 5 }} more...
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-sm text-gray-500">No deals found.</p>
                        @endif
                    </div>
                </div>

                <!-- Activities -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activities ({{ $contact->activities->count() }})</h3>
                        @if($contact->activities->count() > 0)
                            <div class="space-y-2">
                                @foreach($contact->activities->take(5) as $activity)
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ $activity->subject }}</div>
                                        <div class="text-gray-500">{{ $activity->type_display }} - {{ $activity->activity_date->format('M d, Y') }}</div>
                                    </div>
                                @endforeach
                                @if($contact->activities->count() > 5)
                                    <div class="text-sm text-gray-500">
                                        And {{ $contact->activities->count() - 5 }} more...
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-sm text-gray-500">No activities found.</p>
                        @endif
                    </div>
                </div>

                <!-- Tasks -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Tasks ({{ $contact->tasks->count() }})</h3>
                        @if($contact->tasks->count() > 0)
                            <div class="space-y-2">
                                @foreach($contact->tasks->take(5) as $task)
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ $task->name }}</div>
                                        <div class="text-gray-500">{{ $task->status_display }} - {{ $task->due_date ? $task->due_date->format('M d, Y') : 'No due date' }}</div>
                                    </div>
                                @endforeach
                                @if($contact->tasks->count() > 5)
                                    <div class="text-sm text-gray-500">
                                        And {{ $contact->tasks->count() - 5 }} more...
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-sm text-gray-500">No tasks found.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
