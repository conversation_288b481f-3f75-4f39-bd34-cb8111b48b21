<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">👥</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Contacts</div>
                                <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_contacts']) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">🏢</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Companies</div>
                                <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_companies']) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">💼</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Deals</div>
                                <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_deals']) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">💰</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Deal Value</div>
                                <div class="text-2xl font-bold text-gray-900">${{ number_format($stats['total_deal_value']) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Pipeline Overview -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Deal Pipeline</h3>
                        <div class="space-y-3">
                            @foreach($pipelineData as $stage)
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 rounded-full mr-3" style="background-color: {{ $stage['stage']->color }};"></div>
                                        <span class="text-sm font-medium text-gray-900">{{ $stage['stage']->name }}</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900">{{ $stage['count'] }} deals</div>
                                        <div class="text-xs text-gray-500">${{ number_format($stage['value']) }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
                            <a href="{{ route('activities.index') }}" class="text-sm text-blue-600 hover:text-blue-900">View all</a>
                        </div>
                        <div class="space-y-3">
                            @forelse($recentActivities as $activity)
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-6 h-6 rounded-full flex items-center justify-center text-xs
                                            @if($activity->type === 'note') bg-blue-100 text-blue-600
                                            @elseif($activity->type === 'email') bg-green-100 text-green-600
                                            @elseif($activity->type === 'call') bg-yellow-100 text-yellow-600
                                            @elseif($activity->type === 'meeting') bg-purple-100 text-purple-600
                                            @else bg-gray-100 text-gray-600 @endif">
                                            @if($activity->type === 'note') 📝
                                            @elseif($activity->type === 'email') 📧
                                            @elseif($activity->type === 'call') 📞
                                            @elseif($activity->type === 'meeting') 🤝
                                            @else ✅ @endif
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">{{ $activity->subject }}</p>
                                        <p class="text-xs text-gray-500">{{ $activity->activity_date->diffForHumans() }} by {{ $activity->createdBy->name }}</p>
                                    </div>
                                </div>
                            @empty
                                <p class="text-sm text-gray-500">No recent activities.</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Upcoming Tasks -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Upcoming Tasks</h3>
                            <a href="{{ route('tasks.index') }}" class="text-sm text-blue-600 hover:text-blue-900">View all</a>
                        </div>
                        <div class="space-y-3">
                            @forelse($upcomingTasks as $task)
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">{{ $task->name }}</p>
                                        <p class="text-xs text-gray-500">
                                            Due: {{ $task->due_date ? $task->due_date->format('M d, Y') : 'No due date' }}
                                            @if($task->assignedTo) • Assigned to {{ $task->assignedTo->name }} @endif
                                        </p>
                                    </div>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($task->priority === 'low') bg-gray-100 text-gray-800
                                        @elseif($task->priority === 'medium') bg-yellow-100 text-yellow-800
                                        @elseif($task->priority === 'high') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($task->priority) }}
                                    </span>
                                </div>
                            @empty
                                <p class="text-sm text-gray-500">No upcoming tasks.</p>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Overdue Tasks -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Overdue Tasks</h3>
                            <a href="{{ route('tasks.index', ['status' => 'pending']) }}" class="text-sm text-red-600 hover:text-red-900">View all</a>
                        </div>
                        <div class="space-y-3">
                            @forelse($overdueTasks as $task)
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">{{ $task->name }}</p>
                                        <p class="text-xs text-red-500">
                                            Overdue: {{ $task->due_date ? $task->due_date->format('M d, Y') : 'No due date' }}
                                            @if($task->assignedTo) • Assigned to {{ $task->assignedTo->name }} @endif
                                        </p>
                                    </div>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {{ ucfirst($task->priority) }}
                                    </span>
                                </div>
                            @empty
                                <p class="text-sm text-gray-500">No overdue tasks.</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
