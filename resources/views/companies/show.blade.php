<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Company Details: ') . $company->name }}
            </h2>
            <div>
                <a href="{{ route('companies.edit', $company) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Edit Company
                </a>
                <a href="{{ route('companies.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Companies
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Company Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Company Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Company Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Website</dt>
                                    <dd class="text-sm text-gray-900">
                                        @if($company->website)
                                            <a href="{{ $company->website }}" target="_blank" class="text-blue-600 hover:text-blue-900">
                                                {{ $company->website }}
                                            </a>
                                        @else
                                            N/A
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Industry</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->industry ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->phone ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Number of Employees</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->employees ? number_format($company->employees) : 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Annual Revenue</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->revenue ? '$' . number_format($company->revenue) : 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Owner</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->owner->name ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Parent Company</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->parentCompany->name ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->created_at->format('M d, Y H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->updated_at->format('M d, Y H:i') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    @if($company->address)
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500">Address</dt>
                            <dd class="text-sm text-gray-900 mt-1">{{ $company->address }}</dd>
                        </div>
                    @endif

                    @if($company->description)
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="text-sm text-gray-900 mt-1">{{ $company->description }}</dd>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Subsidiaries -->
            @if($company->subsidiaries->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Subsidiaries ({{ $company->subsidiaries->count() }})</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($company->subsidiaries as $subsidiary)
                                <div class="border rounded-lg p-4">
                                    <h4 class="font-medium text-gray-900">{{ $subsidiary->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $subsidiary->industry ?? 'No industry specified' }}</p>
                                    <a href="{{ route('companies.show', $subsidiary) }}" class="text-sm text-blue-600 hover:text-blue-900">View Details</a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Related Records -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Contacts -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Contacts ({{ $company->contacts->count() }})</h3>
                        @if($company->contacts->count() > 0)
                            <div class="space-y-2">
                                @foreach($company->contacts->take(5) as $contact)
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ $contact->full_name }}</div>
                                        <div class="text-gray-500">{{ $contact->job_title ?? 'No title' }}</div>
                                    </div>
                                @endforeach
                                @if($company->contacts->count() > 5)
                                    <div class="text-sm text-gray-500">
                                        And {{ $company->contacts->count() - 5 }} more...
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-sm text-gray-500">No contacts found.</p>
                        @endif
                    </div>
                </div>

                <!-- Deals -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Deals ({{ $company->deals->count() }})</h3>
                        @if($company->deals->count() > 0)
                            <div class="space-y-2">
                                @foreach($company->deals->take(5) as $deal)
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ $deal->name }}</div>
                                        <div class="text-gray-500">${{ number_format($deal->amount, 2) }}</div>
                                    </div>
                                @endforeach
                                @if($company->deals->count() > 5)
                                    <div class="text-sm text-gray-500">
                                        And {{ $company->deals->count() - 5 }} more...
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-sm text-gray-500">No deals found.</p>
                        @endif
                    </div>
                </div>

                <!-- Activities -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activities ({{ $company->activities->count() }})</h3>
                        @if($company->activities->count() > 0)
                            <div class="space-y-2">
                                @foreach($company->activities->take(5) as $activity)
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ $activity->subject }}</div>
                                        <div class="text-gray-500">{{ $activity->type_display }} - {{ $activity->activity_date->format('M d, Y') }}</div>
                                    </div>
                                @endforeach
                                @if($company->activities->count() > 5)
                                    <div class="text-sm text-gray-500">
                                        And {{ $company->activities->count() - 5 }} more...
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-sm text-gray-500">No activities found.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
