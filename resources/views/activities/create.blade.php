<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Log New Activity') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if($relatedEntity)
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <h3 class="text-lg font-medium text-blue-900">
                                Logging activity for: 
                                @if($relatedType === 'contact')
                                    {{ $relatedEntity->full_name }}
                                @elseif($relatedType === 'company')
                                    {{ $relatedEntity->name }}
                                @elseif($relatedType === 'deal')
                                    {{ $relatedEntity->name }}
                                @endif
                            </h3>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('activities.store') }}">
                        @csrf

                        @if($relatedEntity)
                            <input type="hidden" name="related_type" value="App\Models\{{ ucfirst($relatedType) }}">
                            <input type="hidden" name="related_id" value="{{ $relatedId }}">
                            <input type="hidden" name="redirect_to" value="{{ url()->previous() }}">
                        @endif

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Subject -->
                            <div class="md:col-span-2">
                                <x-input-label for="subject" :value="__('Subject')" />
                                <x-text-input id="subject" class="block mt-1 w-full" type="text" name="subject" :value="old('subject')" required autofocus />
                                <x-input-error :messages="$errors->get('subject')" class="mt-2" />
                            </div>

                            <!-- Activity Type -->
                            <div>
                                <x-input-label for="type" :value="__('Activity Type')" />
                                <select name="type" id="type" class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    @foreach($activityTypes as $type)
                                        <option value="{{ $type }}" {{ old('type') == $type ? 'selected' : '' }}>
                                            {{ ucfirst($type) }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('type')" class="mt-2" />
                            </div>

                            <!-- Activity Date -->
                            <div>
                                <x-input-label for="activity_date" :value="__('Activity Date & Time')" />
                                <x-text-input id="activity_date" class="block mt-1 w-full" type="datetime-local" name="activity_date" :value="old('activity_date', now()->format('Y-m-d\TH:i'))" required />
                                <x-input-error :messages="$errors->get('activity_date')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-6">
                            <x-input-label for="description" :value="__('Description')" />
                            <textarea id="description" name="description" rows="4" class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description') }}</textarea>
                            <x-input-error :messages="$errors->get('description')" class="mt-2" />
                        </div>

                        <!-- Outcome -->
                        <div class="mt-6">
                            <x-input-label for="outcome" :value="__('Outcome/Result')" />
                            <textarea id="outcome" name="outcome" rows="3" class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="What was the result of this activity?">{{ old('outcome') }}</textarea>
                            <x-input-error :messages="$errors->get('outcome')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-end mt-6">
                            <a href="{{ $relatedEntity ? url()->previous() : route('activities.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-3">
                                Cancel
                            </a>
                            <x-primary-button>
                                {{ __('Log Activity') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
