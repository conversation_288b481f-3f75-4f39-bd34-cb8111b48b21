<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Activities') }}
            </h2>
            <a href="{{ route('activities.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Log New Activity
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('activities.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}" 
                                   placeholder="Subject or description..." 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
                            <select name="type" id="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Types</option>
                                @foreach($activityTypes as $type)
                                    <option value="{{ $type }}" {{ request('type') == $type ? 'selected' : '' }}>
                                        {{ ucfirst($type) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="created_by" class="block text-sm font-medium text-gray-700">Created By</label>
                            <select name="created_by" id="created_by" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Users</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ request('created_by') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700">Date From</label>
                            <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                                Filter
                            </button>
                            <a href="{{ route('activities.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Activities Timeline -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if($activities->count() > 0)
                        <div class="space-y-6">
                            @foreach($activities as $activity)
                                <div class="flex space-x-4">
                                    <!-- Activity Icon -->
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 rounded-full flex items-center justify-center
                                            @if($activity->type === 'note') bg-blue-100 text-blue-600
                                            @elseif($activity->type === 'email') bg-green-100 text-green-600
                                            @elseif($activity->type === 'call') bg-yellow-100 text-yellow-600
                                            @elseif($activity->type === 'meeting') bg-purple-100 text-purple-600
                                            @else bg-gray-100 text-gray-600 @endif">
                                            @if($activity->type === 'note')
                                                📝
                                            @elseif($activity->type === 'email')
                                                📧
                                            @elseif($activity->type === 'call')
                                                📞
                                            @elseif($activity->type === 'meeting')
                                                🤝
                                            @else
                                                ✅
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Activity Content -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <h3 class="text-lg font-medium text-gray-900">{{ $activity->subject }}</h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        @if($activity->type === 'note') bg-blue-100 text-blue-800
                                                        @elseif($activity->type === 'email') bg-green-100 text-green-800
                                                        @elseif($activity->type === 'call') bg-yellow-100 text-yellow-800
                                                        @elseif($activity->type === 'meeting') bg-purple-100 text-purple-800
                                                        @else bg-gray-100 text-gray-800 @endif">
                                                        {{ ucfirst($activity->type) }}
                                                    </span>
                                                    <span>{{ $activity->activity_date->format('M d, Y H:i') }}</span>
                                                    <span>by {{ $activity->createdBy->name }}</span>
                                                    @if($activity->related)
                                                        <span>
                                                            Related to: 
                                                            @if($activity->related_type === 'App\\Models\\Contact')
                                                                <a href="{{ route('contacts.show', $activity->related) }}" class="text-blue-600 hover:text-blue-900">
                                                                    {{ $activity->related->full_name }}
                                                                </a>
                                                            @elseif($activity->related_type === 'App\\Models\\Company')
                                                                <a href="{{ route('companies.show', $activity->related) }}" class="text-blue-600 hover:text-blue-900">
                                                                    {{ $activity->related->name }}
                                                                </a>
                                                            @elseif($activity->related_type === 'App\\Models\\Deal')
                                                                <a href="{{ route('deals.show', $activity->related) }}" class="text-blue-600 hover:text-blue-900">
                                                                    {{ $activity->related->name }}
                                                                </a>
                                                            @endif
                                                        </span>
                                                    @endif
                                                </div>
                                                
                                                @if($activity->description)
                                                    <p class="text-gray-700 mt-2">{{ $activity->description }}</p>
                                                @endif
                                                
                                                @if($activity->outcome)
                                                    <div class="mt-2">
                                                        <span class="text-sm font-medium text-gray-500">Outcome:</span>
                                                        <p class="text-gray-700">{{ $activity->outcome }}</p>
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Actions -->
                                            <div class="flex space-x-2 ml-4">
                                                <a href="{{ route('activities.show', $activity) }}" class="text-indigo-600 hover:text-indigo-900 text-sm">View</a>
                                                @if(auth()->user()->isAdmin() || $activity->created_by === auth()->id())
                                                    <a href="{{ route('activities.edit', $activity) }}" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</a>
                                                    <form action="{{ route('activities.destroy', $activity) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this activity?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                                                    </form>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-6">
                            {{ $activities->withQueryString()->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <div class="text-gray-500 text-lg">No activities found.</div>
                            <a href="{{ route('activities.create') }}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Log Your First Activity
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
