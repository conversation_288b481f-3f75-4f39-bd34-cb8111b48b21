<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Contact::with(['company', 'owner']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('company', function ($companyQuery) use ($search) {
                      $companyQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('lead_status')) {
            $query->where('lead_status', $request->lead_status);
        }

        if ($request->filled('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        if ($request->filled('owner_id')) {
            $query->where('owner_id', $request->owner_id);
        }

        // Role-based filtering
        if (!Auth::user()->isAdmin()) {
            if (Auth::user()->isSalesManager()) {
                // Sales managers can see their own contacts and their team's contacts
                $query->where('owner_id', Auth::id());
            } else {
                // Sales reps can only see their own contacts
                $query->where('owner_id', Auth::id());
            }
        }

        $contacts = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $companies = Company::orderBy('name')->get();
        $owners = User::orderBy('name')->get();
        $leadStatuses = ['new', 'contacted', 'qualified', 'lost', 'converted'];

        return view('contacts.index', compact('contacts', 'companies', 'owners', 'leadStatuses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $companies = Company::orderBy('name')->get();
        $owners = User::orderBy('name')->get();
        $leadStatuses = ['new', 'contacted', 'qualified', 'lost', 'converted'];

        return view('contacts.create', compact('companies', 'owners', 'leadStatuses'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],
            'job_title' => ['nullable', 'string', 'max:255'],
            'company_id' => ['nullable', 'exists:companies,id'],
            'address' => ['nullable', 'string'],
            'lead_source' => ['nullable', 'string', 'max:255'],
            'lead_status' => ['required', 'in:new,contacted,qualified,lost,converted'],
            'owner_id' => ['nullable', 'exists:users,id'],
            'notes' => ['nullable', 'string'],
        ]);

        $contact = Contact::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'job_title' => $request->job_title,
            'company_id' => $request->company_id,
            'address' => $request->address,
            'lead_source' => $request->lead_source,
            'lead_status' => $request->lead_status,
            'owner_id' => $request->owner_id ?: Auth::id(),
            'notes' => $request->notes,
        ]);

        return redirect()->route('contacts.index')->with('success', 'Contact created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        $contact->load(['company', 'owner', 'deals', 'activities', 'tasks']);

        // Check permissions
        if (!Auth::user()->isAdmin() && $contact->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        return view('contacts.show', compact('contact'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contact $contact)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $contact->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $companies = Company::orderBy('name')->get();
        $owners = User::orderBy('name')->get();
        $leadStatuses = ['new', 'contacted', 'qualified', 'lost', 'converted'];

        return view('contacts.edit', compact('contact', 'companies', 'owners', 'leadStatuses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contact $contact)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $contact->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],
            'job_title' => ['nullable', 'string', 'max:255'],
            'company_id' => ['nullable', 'exists:companies,id'],
            'address' => ['nullable', 'string'],
            'lead_source' => ['nullable', 'string', 'max:255'],
            'lead_status' => ['required', 'in:new,contacted,qualified,lost,converted'],
            'owner_id' => ['nullable', 'exists:users,id'],
            'notes' => ['nullable', 'string'],
        ]);

        $contact->update($request->all());

        return redirect()->route('contacts.index')->with('success', 'Contact updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $contact->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $contact->delete();
        return redirect()->route('contacts.index')->with('success', 'Contact deleted successfully.');
    }
}
