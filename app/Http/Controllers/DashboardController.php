<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Company;
use App\Models\Deal;
use App\Models\Activity;
use App\Models\Task;
use App\Models\DealStage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Get basic counts
        $stats = [
            'total_contacts' => $this->getContactCount(),
            'total_companies' => $this->getCompanyCount(),
            'total_deals' => $this->getDealCount(),
            'total_deal_value' => $this->getTotalDealValue(),
        ];

        // Get recent activities
        $recentActivities = $this->getRecentActivities();

        // Get upcoming tasks
        $upcomingTasks = $this->getUpcomingTasks();

        // Get deal pipeline data
        $pipelineData = $this->getPipelineData();

        // Get overdue tasks
        $overdueTasks = $this->getOverdueTasks();

        return view('dashboard', compact(
            'stats',
            'recentActivities',
            'upcomingTasks',
            'pipelineData',
            'overdueTasks'
        ));
    }

    private function getContactCount()
    {
        $query = Contact::query();

        if (!Auth::user()->isAdmin()) {
            $query->where('owner_id', Auth::id());
        }

        return $query->count();
    }

    private function getCompanyCount()
    {
        $query = Company::query();

        if (!Auth::user()->isAdmin()) {
            $query->where('owner_id', Auth::id());
        }

        return $query->count();
    }

    private function getDealCount()
    {
        $query = Deal::query();

        if (!Auth::user()->isAdmin()) {
            $query->where('owner_id', Auth::id());
        }

        return $query->count();
    }

    private function getTotalDealValue()
    {
        $query = Deal::query();

        if (!Auth::user()->isAdmin()) {
            $query->where('owner_id', Auth::id());
        }

        return $query->sum('amount') ?: 0;
    }

    private function getRecentActivities()
    {
        $query = Activity::with(['related', 'createdBy'])
            ->orderBy('activity_date', 'desc');

        if (!Auth::user()->isAdmin()) {
            $query->where('created_by', Auth::id());
        }

        return $query->limit(5)->get();
    }

    private function getUpcomingTasks()
    {
        $query = Task::with(['related', 'assignedTo'])
            ->where('status', '!=', 'completed')
            ->where('due_date', '>=', today())
            ->orderBy('due_date', 'asc');

        if (!Auth::user()->isAdmin()) {
            $query->where(function ($q) {
                $q->where('assigned_to', Auth::id())
                  ->orWhere('created_by', Auth::id());
            });
        }

        return $query->limit(5)->get();
    }

    private function getOverdueTasks()
    {
        $query = Task::with(['related', 'assignedTo'])
            ->where('status', '!=', 'completed')
            ->where('due_date', '<', today())
            ->orderBy('due_date', 'asc');

        if (!Auth::user()->isAdmin()) {
            $query->where(function ($q) {
                $q->where('assigned_to', Auth::id())
                  ->orWhere('created_by', Auth::id());
            });
        }

        return $query->limit(5)->get();
    }

    private function getPipelineData()
    {
        $stages = DealStage::active()->ordered()->get();
        $pipelineData = [];

        foreach ($stages as $stage) {
            $query = Deal::where('deal_stage_id', $stage->id);

            if (!Auth::user()->isAdmin()) {
                $query->where('owner_id', Auth::id());
            }

            $deals = $query->get();

            $pipelineData[] = [
                'stage' => $stage,
                'count' => $deals->count(),
                'value' => $deals->sum('amount') ?: 0,
            ];
        }

        return $pipelineData;
    }
}
