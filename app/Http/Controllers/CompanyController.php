<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Company::with(['owner', 'parentCompany']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('website', 'like', "%{$search}%")
                  ->orWhere('industry', 'like', "%{$search}%");
            });
        }

        if ($request->filled('industry')) {
            $query->where('industry', $request->industry);
        }

        if ($request->filled('owner_id')) {
            $query->where('owner_id', $request->owner_id);
        }

        // Role-based filtering
        if (!Auth::user()->isAdmin()) {
            if (Auth::user()->isSalesManager()) {
                // Sales managers can see their own companies and their team's companies
                $query->where('owner_id', Auth::id());
            } else {
                // Sales reps can only see their own companies
                $query->where('owner_id', Auth::id());
            }
        }

        $companies = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $industries = Company::distinct()->pluck('industry')->filter()->sort();
        $owners = User::orderBy('name')->get();

        return view('companies.index', compact('companies', 'industries', 'owners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $owners = User::orderBy('name')->get();
        $parentCompanies = Company::orderBy('name')->get();

        return view('companies.create', compact('owners', 'parentCompanies'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'website' => ['nullable', 'url', 'max:255'],
            'industry' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string'],
            'employees' => ['nullable', 'integer', 'min:0'],
            'revenue' => ['nullable', 'numeric', 'min:0'],
            'owner_id' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'string'],
            'parent_company_id' => ['nullable', 'exists:companies,id'],
        ]);

        $company = Company::create([
            'name' => $request->name,
            'website' => $request->website,
            'industry' => $request->industry,
            'phone' => $request->phone,
            'address' => $request->address,
            'employees' => $request->employees,
            'revenue' => $request->revenue,
            'owner_id' => $request->owner_id ?: Auth::id(),
            'description' => $request->description,
            'parent_company_id' => $request->parent_company_id,
        ]);

        return redirect()->route('companies.index')->with('success', 'Company created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Company $company)
    {
        $company->load(['owner', 'parentCompany', 'subsidiaries', 'contacts', 'deals', 'activities', 'tasks']);

        // Check permissions
        if (!Auth::user()->isAdmin() && $company->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        return view('companies.show', compact('company'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Company $company)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $company->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $owners = User::orderBy('name')->get();
        $parentCompanies = Company::where('id', '!=', $company->id)->orderBy('name')->get();

        return view('companies.edit', compact('company', 'owners', 'parentCompanies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Company $company)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $company->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'website' => ['nullable', 'url', 'max:255'],
            'industry' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string'],
            'employees' => ['nullable', 'integer', 'min:0'],
            'revenue' => ['nullable', 'numeric', 'min:0'],
            'owner_id' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'string'],
            'parent_company_id' => ['nullable', 'exists:companies,id', 'not_in:' . $company->id],
        ]);

        $company->update($request->all());

        return redirect()->route('companies.index')->with('success', 'Company updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Company $company)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $company->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        // Check if company has contacts or deals
        if ($company->contacts()->count() > 0 || $company->deals()->count() > 0) {
            return redirect()->route('companies.index')->with('error', 'Cannot delete company with associated contacts or deals.');
        }

        $company->delete();
        return redirect()->route('companies.index')->with('success', 'Company deleted successfully.');
    }
}
