<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\Contact;
use App\Models\Company;
use App\Models\Deal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Activity::with(['related', 'createdBy']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('created_by')) {
            $query->where('created_by', $request->created_by);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('activity_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('activity_date', '<=', $request->date_to);
        }

        // Role-based filtering
        if (!Auth::user()->isAdmin()) {
            $query->where('created_by', Auth::id());
        }

        $activities = $query->orderBy('activity_date', 'desc')->paginate(15);

        // Get filter options
        $activityTypes = ['note', 'email', 'call', 'meeting', 'task'];
        $users = \App\Models\User::orderBy('name')->get();

        return view('activities.index', compact('activities', 'activityTypes', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $activityTypes = ['note', 'email', 'call', 'meeting', 'task'];

        // Get related entity if specified
        $relatedEntity = null;
        $relatedType = $request->get('related_type');
        $relatedId = $request->get('related_id');

        if ($relatedType && $relatedId) {
            switch ($relatedType) {
                case 'contact':
                    $relatedEntity = Contact::find($relatedId);
                    break;
                case 'company':
                    $relatedEntity = Company::find($relatedId);
                    break;
                case 'deal':
                    $relatedEntity = Deal::find($relatedId);
                    break;
            }
        }

        return view('activities.create', compact('activityTypes', 'relatedEntity', 'relatedType', 'relatedId'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'subject' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:note,email,call,meeting,task'],
            'activity_date' => ['required', 'date'],
            'description' => ['nullable', 'string'],
            'outcome' => ['nullable', 'string'],
            'related_type' => ['nullable', 'string'],
            'related_id' => ['nullable', 'integer'],
        ]);

        Activity::create([
            'subject' => $request->subject,
            'type' => $request->type,
            'activity_date' => $request->activity_date,
            'description' => $request->description,
            'outcome' => $request->outcome,
            'related_type' => $request->related_type,
            'related_id' => $request->related_id,
            'created_by' => Auth::id(),
        ]);

        $redirectUrl = $request->get('redirect_to', route('activities.index'));
        return redirect($redirectUrl)->with('success', 'Activity logged successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Activity $activity)
    {
        $activity->load(['related', 'createdBy']);

        // Check permissions
        if (!Auth::user()->isAdmin() && $activity->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        return view('activities.show', compact('activity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Activity $activity)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $activity->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $activityTypes = ['note', 'email', 'call', 'meeting', 'task'];

        return view('activities.edit', compact('activity', 'activityTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Activity $activity)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $activity->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'subject' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:note,email,call,meeting,task'],
            'activity_date' => ['required', 'date'],
            'description' => ['nullable', 'string'],
            'outcome' => ['nullable', 'string'],
        ]);

        $activity->update($request->only([
            'subject', 'type', 'activity_date', 'description', 'outcome'
        ]));

        return redirect()->route('activities.index')->with('success', 'Activity updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Activity $activity)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $activity->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $activity->delete();
        return redirect()->route('activities.index')->with('success', 'Activity deleted successfully.');
    }

    /**
     * Quick log activity via AJAX
     */
    public function quickLog(Request $request)
    {
        $request->validate([
            'subject' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:note,email,call,meeting,task'],
            'description' => ['nullable', 'string'],
            'related_type' => ['nullable', 'string'],
            'related_id' => ['nullable', 'integer'],
        ]);

        $activity = Activity::create([
            'subject' => $request->subject,
            'type' => $request->type,
            'activity_date' => now(),
            'description' => $request->description,
            'related_type' => $request->related_type,
            'related_id' => $request->related_id,
            'created_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'activity' => $activity->load(['createdBy'])
        ]);
    }
}
