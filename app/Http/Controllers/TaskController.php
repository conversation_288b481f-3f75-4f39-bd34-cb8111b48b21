<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Contact;
use App\Models\Company;
use App\Models\Deal;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Task::with(['related', 'assignedTo', 'createdBy']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('due_date_from')) {
            $query->whereDate('due_date', '>=', $request->due_date_from);
        }

        if ($request->filled('due_date_to')) {
            $query->whereDate('due_date', '<=', $request->due_date_to);
        }

        // Role-based filtering
        if (!Auth::user()->isAdmin()) {
            $query->where(function ($q) {
                $q->where('assigned_to', Auth::id())
                  ->orWhere('created_by', Auth::id());
            });
        }

        $tasks = $query->orderBy('due_date', 'asc')->paginate(15);

        // Get filter options
        $statuses = ['pending', 'in_progress', 'completed', 'cancelled'];
        $priorities = ['low', 'medium', 'high', 'urgent'];
        $users = User::orderBy('name')->get();

        return view('tasks.index', compact('tasks', 'statuses', 'priorities', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $priorities = ['low', 'medium', 'high', 'urgent'];
        $statuses = ['pending', 'in_progress', 'completed', 'cancelled'];
        $users = User::orderBy('name')->get();

        // Get related entity if specified
        $relatedEntity = null;
        $relatedType = $request->get('related_type');
        $relatedId = $request->get('related_id');

        if ($relatedType && $relatedId) {
            switch ($relatedType) {
                case 'contact':
                    $relatedEntity = Contact::find($relatedId);
                    break;
                case 'company':
                    $relatedEntity = Company::find($relatedId);
                    break;
                case 'deal':
                    $relatedEntity = Deal::find($relatedId);
                    break;
            }
        }

        return view('tasks.create', compact('priorities', 'statuses', 'users', 'relatedEntity', 'relatedType', 'relatedId'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'due_date' => ['nullable', 'date'],
            'priority' => ['required', 'in:low,medium,high,urgent'],
            'status' => ['required', 'in:pending,in_progress,completed,cancelled'],
            'assigned_to' => ['nullable', 'exists:users,id'],
            'related_type' => ['nullable', 'string'],
            'related_id' => ['nullable', 'integer'],
        ]);

        $task = Task::create([
            'name' => $request->name,
            'description' => $request->description,
            'due_date' => $request->due_date,
            'priority' => $request->priority,
            'status' => $request->status,
            'assigned_to' => $request->assigned_to ?: Auth::id(),
            'related_type' => $request->related_type,
            'related_id' => $request->related_id,
            'created_by' => Auth::id(),
            'completed_at' => $request->status === 'completed' ? now() : null,
        ]);

        $redirectUrl = $request->get('redirect_to', route('tasks.index'));
        return redirect($redirectUrl)->with('success', 'Task created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Task $task)
    {
        $task->load(['related', 'assignedTo', 'createdBy']);

        // Check permissions
        if (!Auth::user()->isAdmin() && $task->assigned_to !== Auth::id() && $task->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        return view('tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Task $task)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $task->assigned_to !== Auth::id() && $task->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $priorities = ['low', 'medium', 'high', 'urgent'];
        $statuses = ['pending', 'in_progress', 'completed', 'cancelled'];
        $users = User::orderBy('name')->get();

        return view('tasks.edit', compact('task', 'priorities', 'statuses', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Task $task)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $task->assigned_to !== Auth::id() && $task->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'due_date' => ['nullable', 'date'],
            'priority' => ['required', 'in:low,medium,high,urgent'],
            'status' => ['required', 'in:pending,in_progress,completed,cancelled'],
            'assigned_to' => ['nullable', 'exists:users,id'],
        ]);

        $updateData = $request->only([
            'name', 'description', 'due_date', 'priority', 'status', 'assigned_to'
        ]);

        // Set completed_at timestamp if status is completed
        if ($request->status === 'completed' && $task->status !== 'completed') {
            $updateData['completed_at'] = now();
        } elseif ($request->status !== 'completed') {
            $updateData['completed_at'] = null;
        }

        $task->update($updateData);

        return redirect()->route('tasks.index')->with('success', 'Task updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Task $task)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $task->assigned_to !== Auth::id() && $task->created_by !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $task->delete();
        return redirect()->route('tasks.index')->with('success', 'Task deleted successfully.');
    }

    /**
     * Mark task as completed
     */
    public function complete(Task $task)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $task->assigned_to !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $task->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        return back()->with('success', 'Task marked as completed.');
    }
}
