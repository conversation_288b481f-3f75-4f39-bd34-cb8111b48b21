<?php

namespace App\Http\Controllers;

use App\Models\Deal;
use App\Models\DealStage;
use App\Models\Company;
use App\Models\Contact;
use App\Models\User;
use App\Models\DealStageHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DealController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $view = $request->get('view', 'pipeline'); // pipeline or list

        if ($view === 'pipeline') {
            return $this->pipelineView($request);
        } else {
            return $this->listView($request);
        }
    }

    /**
     * Pipeline/Kanban view
     */
    private function pipelineView(Request $request)
    {
        $stages = DealStage::active()->ordered()->get();

        $dealsQuery = Deal::with(['company', 'contact', 'owner', 'dealStage']);

        // Role-based filtering
        if (!Auth::user()->isAdmin()) {
            if (Auth::user()->isSalesManager()) {
                $dealsQuery->where('owner_id', Auth::id());
            } else {
                $dealsQuery->where('owner_id', Auth::id());
            }
        }

        // Apply filters
        if ($request->filled('owner_id')) {
            $dealsQuery->where('owner_id', $request->owner_id);
        }

        $deals = $dealsQuery->get()->groupBy('deal_stage_id');

        $owners = User::orderBy('name')->get();

        return view('deals.pipeline', compact('stages', 'deals', 'owners'));
    }

    /**
     * List view
     */
    private function listView(Request $request)
    {
        $query = Deal::with(['company', 'contact', 'owner', 'dealStage']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('company', function ($companyQuery) use ($search) {
                      $companyQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('contact', function ($contactQuery) use ($search) {
                      $contactQuery->where('first_name', 'like', "%{$search}%")
                                   ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('deal_stage_id')) {
            $query->where('deal_stage_id', $request->deal_stage_id);
        }

        if ($request->filled('owner_id')) {
            $query->where('owner_id', $request->owner_id);
        }

        // Role-based filtering
        if (!Auth::user()->isAdmin()) {
            if (Auth::user()->isSalesManager()) {
                $query->where('owner_id', Auth::id());
            } else {
                $query->where('owner_id', Auth::id());
            }
        }

        $deals = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $dealStages = DealStage::active()->ordered()->get();
        $owners = User::orderBy('name')->get();

        return view('deals.index', compact('deals', 'dealStages', 'owners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $companies = Company::orderBy('name')->get();
        $contacts = Contact::orderBy('first_name')->get();
        $dealStages = DealStage::active()->ordered()->get();
        $owners = User::orderBy('name')->get();

        return view('deals.create', compact('companies', 'contacts', 'dealStages', 'owners'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'company_id' => ['nullable', 'exists:companies,id'],
            'contact_id' => ['nullable', 'exists:contacts,id'],
            'deal_stage_id' => ['required', 'exists:deal_stages,id'],
            'close_date' => ['nullable', 'date'],
            'amount' => ['nullable', 'numeric', 'min:0'],
            'probability' => ['nullable', 'integer', 'min:0', 'max:100'],
            'owner_id' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'string'],
            'products_services' => ['nullable', 'string'],
        ]);

        $deal = Deal::create([
            'name' => $request->name,
            'company_id' => $request->company_id,
            'contact_id' => $request->contact_id,
            'deal_stage_id' => $request->deal_stage_id,
            'close_date' => $request->close_date,
            'amount' => $request->amount,
            'probability' => $request->probability,
            'owner_id' => $request->owner_id ?: Auth::id(),
            'description' => $request->description,
            'products_services' => $request->products_services,
        ]);

        // Log stage history
        DealStageHistory::create([
            'deal_id' => $deal->id,
            'from_stage_id' => null,
            'to_stage_id' => $deal->deal_stage_id,
            'changed_by' => Auth::id(),
            'notes' => 'Deal created',
        ]);

        return redirect()->route('deals.index')->with('success', 'Deal created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Deal $deal)
    {
        $deal->load(['company', 'contact', 'owner', 'dealStage', 'activities', 'tasks', 'stageHistory.fromStage', 'stageHistory.toStage', 'stageHistory.changedBy']);

        // Check permissions
        if (!Auth::user()->isAdmin() && $deal->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        return view('deals.show', compact('deal'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Deal $deal)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $deal->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $companies = Company::orderBy('name')->get();
        $contacts = Contact::orderBy('first_name')->get();
        $dealStages = DealStage::active()->ordered()->get();
        $owners = User::orderBy('name')->get();

        return view('deals.edit', compact('deal', 'companies', 'contacts', 'dealStages', 'owners'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Deal $deal)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $deal->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'company_id' => ['nullable', 'exists:companies,id'],
            'contact_id' => ['nullable', 'exists:contacts,id'],
            'deal_stage_id' => ['required', 'exists:deal_stages,id'],
            'close_date' => ['nullable', 'date'],
            'amount' => ['nullable', 'numeric', 'min:0'],
            'probability' => ['nullable', 'integer', 'min:0', 'max:100'],
            'owner_id' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'string'],
            'products_services' => ['nullable', 'string'],
            'reason_won_lost' => ['nullable', 'in:won,lost,pending'],
            'reason_won_lost_description' => ['nullable', 'string'],
        ]);

        $oldStageId = $deal->deal_stage_id;
        $deal->update($request->all());

        // Log stage change if stage was updated
        if ($oldStageId != $deal->deal_stage_id) {
            DealStageHistory::create([
                'deal_id' => $deal->id,
                'from_stage_id' => $oldStageId,
                'to_stage_id' => $deal->deal_stage_id,
                'changed_by' => Auth::id(),
                'notes' => $request->get('stage_change_notes', 'Stage updated'),
            ]);
        }

        return redirect()->route('deals.index')->with('success', 'Deal updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Deal $deal)
    {
        // Check permissions
        if (!Auth::user()->isAdmin() && $deal->owner_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        $deal->delete();
        return redirect()->route('deals.index')->with('success', 'Deal deleted successfully.');
    }

    /**
     * Update deal stage via AJAX (for pipeline drag & drop)
     */
    public function updateStage(Request $request, Deal $deal)
    {
        $request->validate([
            'deal_stage_id' => ['required', 'exists:deal_stages,id'],
        ]);

        // Check permissions
        if (!Auth::user()->isAdmin() && $deal->owner_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $oldStageId = $deal->deal_stage_id;
        $deal->update(['deal_stage_id' => $request->deal_stage_id]);

        // Log stage change
        DealStageHistory::create([
            'deal_id' => $deal->id,
            'from_stage_id' => $oldStageId,
            'to_stage_id' => $deal->deal_stage_id,
            'changed_by' => Auth::id(),
            'notes' => 'Stage updated via pipeline',
        ]);

        return response()->json(['success' => true]);
    }
}
