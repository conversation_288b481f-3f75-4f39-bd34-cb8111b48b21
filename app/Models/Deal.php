<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Deal extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'company_id',
        'contact_id',
        'deal_stage_id',
        'close_date',
        'amount',
        'probability',
        'owner_id',
        'description',
        'products_services',
        'reason_won_lost',
        'reason_won_lost_description',
    ];

    protected $casts = [
        'close_date' => 'date',
        'amount' => 'decimal:2',
        'probability' => 'integer',
    ];

    // Relationships
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function dealStage()
    {
        return $this->belongsTo(DealStage::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function activities()
    {
        return $this->morphMany(Activity::class, 'related');
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'related');
    }

    public function stageHistory()
    {
        return $this->hasMany(DealStageHistory::class);
    }

    public function customFieldValues()
    {
        return $this->morphMany(CustomFieldValue::class, 'entity');
    }

    // Scopes
    public function scopeByStage($query, $stageId)
    {
        return $query->where('deal_stage_id', $stageId);
    }

    public function scopeByOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    public function scopeOpen($query)
    {
        return $query->whereHas('dealStage', function ($q) {
            $q->where('name', '!=', 'Closed Won')
              ->where('name', '!=', 'Closed Lost');
        });
    }

    public function scopeWon($query)
    {
        return $query->whereHas('dealStage', function ($q) {
            $q->where('name', 'Closed Won');
        });
    }

    public function scopeLost($query)
    {
        return $query->whereHas('dealStage', function ($q) {
            $q->where('name', 'Closed Lost');
        });
    }
}
