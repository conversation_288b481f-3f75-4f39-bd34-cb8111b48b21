<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomFieldValue extends Model
{
    use HasFactory;

    protected $fillable = [
        'custom_field_id',
        'entity_type',
        'entity_id',
        'value',
    ];

    // Relationships
    public function customField()
    {
        return $this->belongsTo(CustomField::class);
    }

    public function entity()
    {
        return $this->morphTo();
    }

    // Accessors
    public function getFormattedValueAttribute()
    {
        $field = $this->customField;

        if (!$field) {
            return $this->value;
        }

        switch ($field->type) {
            case 'date':
                return $this->value ? \Carbon\Carbon::parse($this->value)->format('Y-m-d') : null;
            case 'number':
                return is_numeric($this->value) ? (float) $this->value : $this->value;
            case 'checkbox':
                return (bool) $this->value;
            default:
                return $this->value;
        }
    }
}
