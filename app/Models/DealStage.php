<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DealStage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'probability',
        'color',
        'order',
        'is_active',
    ];

    protected $casts = [
        'probability' => 'integer',
        'order' => 'integer',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function deals()
    {
        return $this->hasMany(Deal::class);
    }

    public function stageHistoryFrom()
    {
        return $this->hasMany(DealStageHistory::class, 'from_stage_id');
    }

    public function stageHistoryTo()
    {
        return $this->hasMany(DealStageHistory::class, 'to_stage_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }
}
