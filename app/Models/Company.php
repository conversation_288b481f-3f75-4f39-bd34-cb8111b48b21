<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'website',
        'industry',
        'phone',
        'address',
        'employees',
        'revenue',
        'owner_id',
        'description',
        'parent_company_id',
    ];

    protected $casts = [
        'revenue' => 'decimal:2',
        'employees' => 'integer',
    ];

    // Relationships
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function parentCompany()
    {
        return $this->belongsTo(Company::class, 'parent_company_id');
    }

    public function subsidiaries()
    {
        return $this->hasMany(Company::class, 'parent_company_id');
    }

    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    public function deals()
    {
        return $this->hasMany(Deal::class);
    }

    public function activities()
    {
        return $this->morphMany(Activity::class, 'related');
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'related');
    }

    public function customFieldValues()
    {
        return $this->morphMany(CustomFieldValue::class, 'entity');
    }
}
