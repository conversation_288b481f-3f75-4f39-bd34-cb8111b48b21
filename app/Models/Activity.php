<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory;

    protected $fillable = [
        'subject',
        'type',
        'activity_date',
        'description',
        'outcome',
        'related_type',
        'related_id',
        'created_by',
    ];

    protected $casts = [
        'activity_date' => 'datetime',
    ];

    // Relationships
    public function related()
    {
        return $this->morphTo();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('created_by', $userId);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('activity_date', '>=', now()->subDays($days));
    }

    public function scopeUpcoming($query)
    {
        return $query->where('activity_date', '>', now());
    }

    // Accessors
    public function getTypeDisplayAttribute()
    {
        return ucfirst($this->type);
    }
}
